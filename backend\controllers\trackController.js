const Visit = require('../models/Visit');

// POST /api/track/visit
exports.logVisit = async (req, res) => {
  const { section, duration = 0, sessionId = '', pageUrl = '' } = req.body;
  const ip = req.visitorIP || req.ip;
  const userAgent = req.get('User-Agent') || '';

  if (!section) return res.status(400).json({ message: 'Section is required' });

  try {
    await Visit.create({
      ip,
      section,
      duration: Math.max(0, Math.floor(duration)), // Ensure positive integer
      userAgent,
      sessionId,
      pageUrl
    });
    res.status(201).json({ message: 'Visit logged' });
  } catch (err) {
    console.error('Visit logging error:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// GET /api/track/section-details - Get detailed section analytics
exports.getSectionDetails = async (req, res) => {
  try {
    // Get detailed visitor behavior grouped by IP and section
    const detailedStats = await Visit.aggregate([
      {
        $group: {
          _id: { ip: '$ip', section: '$section' },
          totalDuration: { $sum: '$duration' },
          visitCount: { $sum: 1 },
          lastVisit: { $max: '$timestamp' },
          avgDuration: { $avg: '$duration' },
          pageUrls: { $addToSet: '$pageUrl' }
        }
      },
      {
        $group: {
          _id: '$_id.ip',
          sections: {
            $push: {
              section: '$_id.section',
              totalDuration: '$totalDuration',
              visitCount: '$visitCount',
              lastVisit: '$lastVisit',
              avgDuration: '$avgDuration',
              pageUrls: '$pageUrls'
            }
          },
          totalTimeSpent: { $sum: '$totalDuration' }
        }
      },
      {
        $sort: { totalTimeSpent: -1 }
      },
      {
        $limit: 50 // Limit to top 50 most active visitors
      }
    ]);

    // Format the response for better readability
    const formattedStats = detailedStats.map(visitor => ({
      ip: visitor._id,
      totalTimeSpent: visitor.totalTimeSpent,
      sections: visitor.sections.map(section => ({
        ...section,
        totalDuration: Math.round(section.totalDuration),
        avgDuration: Math.round(section.avgDuration),
        pageUrls: section.pageUrls.filter(url => url && url.trim() !== '')
      })).sort((a, b) => b.totalDuration - a.totalDuration)
    }));

    res.json({
      success: true,
      data: formattedStats,
      totalVisitors: formattedStats.length
    });
  } catch (err) {
    console.error('Section details error:', err);
    res.status(500).json({ message: 'Server error' });
  }
};